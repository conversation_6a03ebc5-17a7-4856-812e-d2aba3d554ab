{"name": "Caribbean Advantage TV - Next.js", "description": "Ultra-modern Caribbean entertainment platform with Next.js, featuring Gaming, Live TV, and Radio streaming with stunning animations", "repository": "https://github.com/joelgriiyo/caribbeanadvantage", "logo": "https://nextjs.org/static/favicon/favicon-32x32.png", "keywords": ["nextjs", "react", "caribbean", "tv", "streaming", "gaming", "framer-motion", "tailwindcss", "entertainment"], "image": "hero<PERSON>/nodejs", "stack": "heroku-22", "buildpacks": [{"url": "hero<PERSON>/nodejs"}], "formation": {"web": {"quantity": 1, "size": "basic"}}, "env": {"NODE_ENV": {"description": "Node environment", "value": "production"}, "NPM_CONFIG_PRODUCTION": {"description": "Skip dev dependencies", "value": "false"}}, "scripts": {"postdeploy": "echo 'Caribbean Advantage TV Next.js deployed successfully!'"}, "success_url": "/", "website": "https://caribbeanadvantage.herokuapp.com"}