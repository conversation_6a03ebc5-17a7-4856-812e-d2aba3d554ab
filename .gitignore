# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
# Note: package-lock.json should be committed for reproducible builds

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript v1 declaration files
typings/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler cache
.cache
.parcel-cache

# next.js build output
.next
out/

# TypeScript build info
*.tsbuildinfo
tsconfig.tsbuildinfo

# nuxt.js build output
.nuxt
dist/

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs
*.log

# Temporary files
tmp/
temp/

# Build artifacts
build/
dist/

# Database
*.db
*.sqlite
*.sqlite3

# Backup files
*.backup
*.bak

# Old HTML files (moved to views/)
/*.html

# Upload directories
uploads/
public/uploads/

# SSL certificates
*.pem
*.key
*.crt

# Local development
.local
