'use client'

import { motion } from 'framer-motion'

export function FloatingElements() {
  // Reduced to just 3 subtle elements
  const elements = [
    { emoji: '🌊', size: 'text-2xl', delay: 0, position: { left: '10%', top: '20%' } },
    { emoji: '🌴', size: 'text-2xl', delay: 4, position: { right: '15%', top: '60%' } },
    { emoji: '📺', size: 'text-xl', delay: 8, position: { left: '80%', bottom: '30%' } },
  ]

  return (
    <div className="fixed inset-0 pointer-events-none z-0 overflow-hidden">
      {elements.map((element, index) => (
        <motion.div
          key={index}
          className={`absolute ${element.size} opacity-10 select-none`}
          style={element.position}
          animate={{
            y: [0, -20, 0],
            rotate: [0, 5, -5, 0],
            scale: [1, 1.05, 1],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut",
            delay: element.delay,
          }}
        >
          {element.emoji}
        </motion.div>
      ))}
    </div>
  )
}
