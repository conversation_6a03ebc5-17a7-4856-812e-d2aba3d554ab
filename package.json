{"name": "caribbean-advantage-tv-nextjs", "version": "2.0.0", "private": true, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "npm run type-check && npm run lint", "clean": "rmdir /s /q .next 2>nul || echo Clean completed", "deploy": "npm run test && npm run build", "heroku-postbuild": "npm run build"}, "dependencies": {"framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "next": "^14.2.29", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/node": "^20.10.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18"}, "devDependencies": {"autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.3.3", "eslint": "^8.56.0", "eslint-config-next": "^14.2.29"}}