// Caribbean Advantage TV - Clean Main JavaScript

// Global variables
let currentLanguage = 'en';

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Caribbean Advantage Channel 99-9 - Loading...');

    initializeLanguageSystem();
    initializeMobileMenu();
    initializeGameModals();

    console.log('✅ Caribbean Advantage Channel 99-9 - Ready!');
});

// Language System
function initializeLanguageSystem() {
    // Load saved language or default to English
    currentLanguage = localStorage.getItem('selectedLanguage') || 'en';

    // Initialize i18n if available
    if (window.CaribbeanAdvantageI18n) {
        window.CaribbeanAdvantageI18n.init();
        updateLanguageDisplay();
    }

    // Setup language dropdown
    const languageToggle = document.getElementById('language-toggle');
    const languageDropdown = document.getElementById('language-dropdown');

    if (languageToggle && languageDropdown) {
        languageToggle.addEventListener('click', function(e) {
            e.preventDefault();
            languageDropdown.classList.toggle('hidden');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!languageToggle.contains(e.target) && !languageDropdown.contains(e.target)) {
                languageDropdown.classList.add('hidden');
            }
        });
    }
}

function changeLanguage(lang) {
    currentLanguage = lang;
    localStorage.setItem('selectedLanguage', lang);

    if (window.CaribbeanAdvantageI18n) {
        window.CaribbeanAdvantageI18n.changeLanguage(lang);
    }

    updateLanguageDisplay();

    // Hide dropdown
    const dropdown = document.getElementById('language-dropdown');
    if (dropdown) {
        dropdown.classList.add('hidden');
    }
}

function updateLanguageDisplay() {
    const currentLangElement = document.getElementById('current-language');
    const langNames = {
        'en': 'English',
        'es': 'Español',
        'fr': 'Français',
        'sw': 'Kiswahili'
    };

    if (currentLangElement) {
        currentLangElement.textContent = langNames[currentLanguage] || 'English';
    }
}

// Mobile Menu
function initializeMobileMenu() {
    const menuToggle = document.getElementById('menu-toggle');
    const mobileMenu = document.getElementById('mobile-menu');

    if (menuToggle && mobileMenu) {
        menuToggle.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
        });

        // Close mobile menu when clicking on links
        const mobileLinks = mobileMenu.querySelectorAll('a');
        mobileLinks.forEach(link => {
            link.addEventListener('click', function() {
                mobileMenu.classList.add('hidden');
            });
        });
    }
}

// Game Modals
function initializeGameModals() {
    // Close modals with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeAllGameModals();
        }
    });

    // Close modals when clicking outside
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('game-modal')) {
            closeAllGameModals();
        }
    });
}

function openGameModal(gameId) {
    const modal = document.getElementById(gameId + '-modal');
    if (modal) {
        modal.classList.add('active');
        document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }
}

function closeGameModal(gameId) {
    const modal = document.getElementById(gameId + '-modal');
    if (modal) {
        modal.classList.remove('active');
        document.body.style.overflow = ''; // Restore scrolling

        // Stop the game by reloading the iframe
        const iframe = modal.querySelector('iframe');
        if (iframe) {
            const src = iframe.src;
            iframe.src = '';
            setTimeout(() => {
                iframe.src = src;
            }, 100);
        }
    }
}

function closeAllGameModals() {
    const modals = document.querySelectorAll('.game-modal');
    modals.forEach(modal => {
        modal.classList.remove('active');

        // Stop all games
        const iframe = modal.querySelector('iframe');
        if (iframe) {
            const src = iframe.src;
            iframe.src = '';
            setTimeout(() => {
                iframe.src = src;
            }, 100);
        }
    });

    document.body.style.overflow = ''; // Restore scrolling
}

// Export functions for global use
window.changeLanguage = changeLanguage;
window.openGameModal = openGameModal;
window.closeGameModal = closeGameModal;




