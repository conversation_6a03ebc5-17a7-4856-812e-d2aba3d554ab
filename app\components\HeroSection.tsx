'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import { Play, Gamepad2, Radio, Sparkles } from 'lucide-react'

import { useLanguage } from './LanguageProvider'

export function HeroSection() {
  const { t } = useLanguage()

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  }

  const floatingVariants = {
    animate: {
      y: [-10, 10, -10],
      rotate: [0, 5, -5, 0],
      transition: {
        duration: 4,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  }

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden pt-20">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Floating Orbs */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-32 h-32 rounded-full opacity-20"
            style={{
              background: `radial-gradient(circle, ${
                ['#0087ff', '#339fff', '#66b7ff', '#00ccff', '#0087ff', '#339fff'][i]
              }, transparent)`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              x: [0, 100, 0],
              y: [0, -100, 0],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 10 + i * 2,
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 0.5
            }}
          />
        ))}

        {/* Geometric Shapes */}
        <motion.div
          className="absolute top-1/4 left-1/4 w-20 h-20 border-2 border-pepsi-blue/30"
          animate={{ rotate: 360 }}
          transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-16 h-16 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full"
          animate={{ scale: [1, 1.5, 1], opacity: [0.3, 0.7, 0.3] }}
          transition={{ duration: 3, repeat: Infinity }}
        />
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="text-center max-w-6xl mx-auto"
        >
          {/* Main Title */}
          <motion.div variants={itemVariants} className="mb-8">
            <motion.h1 
              className="text-6xl md:text-8xl font-black mb-6 leading-tight"
              style={{
                background: 'linear-gradient(135deg, #ffffff, #0087ff, #339fff)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
              }}
            >
              {t('hero.title')}
            </motion.h1>
            
            <motion.div
              className="flex items-center justify-center space-x-4 mb-6"
              variants={itemVariants}
            >
              <motion.div
                className="flex items-center space-x-2 glass-effect px-4 py-2 rounded-full"
                whileHover={{ scale: 1.05 }}
              >
                <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse" />
                <span className="text-sm font-semibold">{t('live.status')}</span>
              </motion.div>
              
              <motion.div
                className="flex items-center space-x-2 glass-effect px-4 py-2 rounded-full"
                whileHover={{ scale: 1.05 }}
              >
                <Sparkles className="w-4 h-4 text-yellow-400" />
                <span className="text-sm font-semibold">{t('live.channel')}</span>
              </motion.div>
            </motion.div>
          </motion.div>

          {/* Subtitle */}
          <motion.p
            variants={itemVariants}
            className="text-xl md:text-2xl text-blue-100 mb-8 max-w-4xl mx-auto leading-relaxed text-shadow"
          >
            {t('hero.subtitle')}
          </motion.p>

          <motion.p
            variants={itemVariants}
            className="text-lg text-blue-200/80 mb-12 max-w-3xl mx-auto"
          >
            {t('hero.description')}
          </motion.p>

          {/* Action Buttons */}
          <motion.div
            variants={itemVariants}
            className="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-6 mb-16"
          >
            <motion.div
              whileHover={{ scale: 1.05, y: -5 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link
                href="/live"
                className="group relative overflow-hidden bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-bold py-4 px-8 rounded-2xl transition-all duration-300 flex items-center space-x-3 shadow-2xl"
              >
                <Play className="w-6 h-6 group-hover:scale-110 transition-transform" />
                <span className="text-lg">{t('hero.watchLive')}</span>
                <motion.div
                  className="absolute inset-0 bg-white/20"
                  initial={{ x: '-100%' }}
                  whileHover={{ x: '100%' }}
                  transition={{ duration: 0.6 }}
                />
              </Link>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05, y: -5 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link
                href="/gamezone"
                className="group relative overflow-hidden btn-secondary font-bold py-4 px-8 rounded-2xl flex items-center space-x-3 shadow-2xl"
              >
                <Gamepad2 className="w-6 h-6 group-hover:scale-110 transition-transform" />
                <span className="text-lg">{t('hero.exploreGames')}</span>
              </Link>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05, y: -5 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link
                href="/radio"
                className="group relative overflow-hidden btn-secondary font-bold py-4 px-8 rounded-2xl flex items-center space-x-3 shadow-2xl"
              >
                <Radio className="w-6 h-6 group-hover:scale-110 transition-transform" />
                <span className="text-lg">{t('hero.listenRadio')}</span>
              </Link>
            </motion.div>
          </motion.div>

          {/* Floating Feature Cards */}
          <motion.div
            variants={itemVariants}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto"
          >
            {[
              { icon: Gamepad2, title: 'Gaming', color: 'from-pepsi-blue to-pepsi-lightBlue' },
              { icon: Play, title: 'Live TV', color: 'from-pepsi-darkBlue to-pepsi-blue' },
              { icon: Radio, title: 'Radio', color: 'from-pepsi-lightBlue to-pepsi-accent' }
            ].map((item, index) => (
              <motion.div
                key={item.title}
                variants={floatingVariants}
                animate="animate"
                style={{ animationDelay: `${index * 0.5}s` }}
                className="glass-card group hover:glow-effect transition-all duration-500 cursor-pointer"
                whileHover={{ scale: 1.05, y: -10 }}
              >
                <div className={`w-16 h-16 bg-gradient-to-r ${item.color} rounded-2xl flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform`}>
                  <item.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white mb-2">{item.title}</h3>
                <div className="w-12 h-1 bg-gradient-to-r from-pepsi-blue to-pepsi-lightBlue rounded-full mx-auto" />
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        animate={{ y: [0, 10, 0] }}
        transition={{ duration: 2, repeat: Infinity }}
      >
        <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
          <motion.div
            className="w-1 h-3 bg-white rounded-full mt-2"
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
          />
        </div>
      </motion.div>
    </section>
  )
}
