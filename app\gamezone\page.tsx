'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Gamepad2,
  Trophy,
  Users,
  Star,
  Play,
  Zap,
  Maximize,
  Share2
} from 'lucide-react'

import { Navigation } from '../components/Navigation'
import { Footer } from '../components/Footer'
import { FloatingElements } from '../components/FloatingElements'
import { useLanguage } from '../components/LanguageProvider'

export default function GameZone() {
  const { t } = useLanguage()
  const [selectedGame, setSelectedGame] = useState('GuaraMania')

  const games = [
    {
      title: 'GuaraMania',
      url: '/Games/GuaraMania/Build/index.html',
      description: 'Caribbean adventure game',
      players: '2.1K',
      rating: '4.8'
    },
    {
      title: 'Caribbean Quest',
      url: 'https://caribbeanadvantage.com/index.php/gamezone',
      description: 'Explore the beautiful Caribbean islands',
      players: '1.8K',
      rating: '4.7'
    }
  ]

  return (
    <main className="relative min-h-screen">
      <Navigation />
      <FloatingElements />

      <div className="relative z-10 pt-20">
        {/* Hero Section */}
        <motion.section
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="container mx-auto px-4 py-8 text-center"
        >
          <motion.div
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, duration: 0.6 }}
            className="mb-8"
          >
            <Gamepad2 className="w-16 h-16 mx-auto mb-4 text-pepsi-blue" />
            <h1 className="text-4xl md:text-5xl font-bold mb-4 gradient-text">
              🎮 GameZone
            </h1>
            <p className="text-lg text-gray-300 max-w-2xl mx-auto">
              Experience the ultimate Caribbean gaming hub with exciting games and tournaments
            </p>
          </motion.div>
        </motion.section>

        {/* Main Content with Sidebars */}
        <div className="container mx-auto px-4 py-4">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">

            {/* Left Promotional Banner */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4, duration: 0.6 }}
              className="lg:col-span-2 space-y-4"
            >
              {/* Promotion Banner 1 */}
              <div className="glass-card p-3 text-center hover:glow-effect transition-all duration-300">
                <div className="bg-gradient-to-br from-pepsi-blue to-pepsi-lightBlue rounded-lg p-3 mb-2">
                  <Trophy className="w-6 h-6 mx-auto text-white mb-1" />
                  <h3 className="text-white font-bold text-xs">Weekly Tournament</h3>
                </div>
                <p className="text-xs text-gray-300 mb-2">Join our weekly gaming tournament!</p>
                <button className="bg-pepsi-blue hover:bg-pepsi-darkBlue text-white text-xs px-2 py-1 rounded transition-colors">
                  Join Now
                </button>
              </div>

              {/* Promotion Banner 2 */}
              <div className="glass-card p-3 text-center hover:glow-effect transition-all duration-300">
                <div className="bg-gradient-to-br from-green-500 to-green-600 rounded-lg p-3 mb-2">
                  <Star className="w-6 h-6 mx-auto text-white mb-1" />
                  <h3 className="text-white font-bold text-xs">VIP Gaming</h3>
                </div>
                <p className="text-xs text-gray-300 mb-2">Unlock exclusive games!</p>
                <button className="bg-green-500 hover:bg-green-600 text-white text-xs px-2 py-1 rounded transition-colors">
                  Upgrade
                </button>
              </div>
            </motion.div>

            {/* Main Game Area */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.6 }}
              className="lg:col-span-8"
            >
              {/* Game Screen */}
              <div className="glass-card p-4 mb-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold text-white flex items-center">
                    <Gamepad2 className="w-5 h-5 mr-2 text-pepsi-blue" />
                    Game Screen
                  </h2>
                  <div className="flex space-x-2">
                    <select
                      value={selectedGame}
                      onChange={(e) => setSelectedGame(e.target.value)}
                      className="bg-pepsi-darkBlue text-white px-3 py-1 rounded text-sm"
                    >
                      {games.map((game) => (
                        <option key={game.title} value={game.title}>
                          {game.title}
                        </option>
                      ))}
                    </select>
                    <button className="bg-pepsi-blue hover:bg-pepsi-darkBlue text-white px-3 py-1 rounded text-sm transition-colors">
                      <Play className="w-3 h-3 inline mr-1" />
                      Play
                    </button>
                    <button className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm transition-colors">
                      <Maximize className="w-3 h-3 inline mr-1" />
                      Fullscreen
                    </button>
                  </div>
                </div>

                {/* Game Iframe */}
                <div className="relative bg-black rounded-lg overflow-hidden" style={{ aspectRatio: '16/9' }}>
                  <iframe
                    src={games.find(g => g.title === selectedGame)?.url || games[0].url}
                    className="w-full h-full border-0"
                    title={`${selectedGame} - Caribbean Advantage Games`}
                    allowFullScreen
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent pointer-events-none" />
                </div>

                <div className="mt-3 flex items-center justify-between text-sm text-gray-400">
                  <div className="flex items-center space-x-4">
                    <span className="flex items-center">
                      <Users className="w-3 h-3 mr-1" />
                      {games.find(g => g.title === selectedGame)?.players || '0'} playing
                    </span>
                    <span className="flex items-center">
                      <Star className="w-3 h-3 mr-1 text-yellow-400" />
                      {games.find(g => g.title === selectedGame)?.rating || '0'}/5
                    </span>
                  </div>
                  <button className="text-pepsi-blue hover:text-pepsi-lightBlue transition-colors flex items-center">
                    <Share2 className="w-3 h-3 mr-1" />
                    Share Game
                  </button>
                </div>
              </div>

              {/* Game Stats */}
              <div className="grid grid-cols-3 gap-4">
                {[
                  { icon: Users, label: 'Players', value: '12.5K+', color: 'text-pepsi-blue' },
                  { icon: Trophy, label: 'Tournaments', value: '45+', color: 'text-yellow-400' },
                  { icon: Star, label: 'Rating', value: '4.8', color: 'text-green-400' }
                ].map((stat, index) => (
                  <div key={index} className="glass-card p-3 text-center">
                    <stat.icon className={`w-6 h-6 ${stat.color} mx-auto mb-2`} />
                    <div className="text-lg font-bold text-white">{stat.value}</div>
                    <div className="text-xs text-gray-400">{stat.label}</div>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Right Promotional Banner */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.8, duration: 0.6 }}
              className="lg:col-span-2 space-y-4"
            >
              {/* Promotion Banner 3 */}
              <div className="glass-card p-3 text-center hover:glow-effect transition-all duration-300">
                <div className="bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg p-3 mb-2">
                  <Gamepad2 className="w-6 h-6 mx-auto text-white mb-1" />
                  <h3 className="text-white font-bold text-xs">New Games</h3>
                </div>
                <p className="text-xs text-gray-300 mb-2">Check out our latest releases!</p>
                <button className="bg-purple-500 hover:bg-purple-600 text-white text-xs px-2 py-1 rounded transition-colors">
                  Explore
                </button>
              </div>

              {/* Promotion Banner 4 */}
              <div className="glass-card p-3 text-center hover:glow-effect transition-all duration-300">
                <div className="bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg p-3 mb-2">
                  <Zap className="w-6 h-6 mx-auto text-white mb-1" />
                  <h3 className="text-white font-bold text-xs">Power-Ups</h3>
                </div>
                <p className="text-xs text-gray-300 mb-2">Boost your gaming experience!</p>
                <button className="bg-orange-500 hover:bg-orange-600 text-white text-xs px-2 py-1 rounded transition-colors">
                  Get Now
                </button>
              </div>

              {/* Live Stream Banner */}
              <div className="glass-card p-3 text-center hover:glow-effect transition-all duration-300">
                <div className="bg-gradient-to-br from-red-500 to-red-600 rounded-lg p-3 mb-2">
                  <div className="flex items-center justify-center mb-1">
                    <div className="w-2 h-2 bg-red-300 rounded-full animate-pulse mr-2"></div>
                    <span className="text-white font-bold text-xs">LIVE</span>
                  </div>
                  <h3 className="text-white font-bold text-xs">Gaming Stream</h3>
                </div>
                <p className="text-xs text-gray-300 mb-2">Watch live gaming sessions!</p>
                <button className="bg-red-500 hover:bg-red-600 text-white text-xs px-2 py-1 rounded transition-colors">
                  Watch Now
                </button>
              </div>
            </motion.div>
          </div>
        </div>

        <Footer />
      </div>
    </main>
  )
}
