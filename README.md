# Caribbean Advantage TV - Channel 99-9 🌴📺

A modern, ultra-responsive Caribbean entertainment platform built with Next.js, featuring Gaming, Live TV, and Radio streaming with stunning animations and multi-language support.

## 🚀 Features

- **🎮 GameZone**: Interactive gaming hub with Caribbean-themed games
- **📺 Live TV**: 24/7 streaming of Caribbean Advantage Channel 99-9
- **📻 Online Radio**: Caribbean music, talk shows, and cultural programming
- **🌍 Multi-language**: English and Spanish support
- **📱 Responsive Design**: Optimized for all devices
- **✨ Modern Animations**: Framer Motion powered interactions
- **🎨 Glassmorphism UI**: Beautiful modern design with glass effects
- **⚡ Performance Optimized**: Built for speed and SEO

## 🛠️ Tech Stack

- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **Deployment**: Heroku Ready

## 🏃‍♂️ Quick Start

### Prerequisites

- Node.js 18+ 
- npm 8+

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/joelgriiyo/caribbeanadvantage.git
   cd caribbeanadvantage
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Run development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📦 Build & Deploy

### Local Production Build
```bash
npm run build
npm start
```

### Heroku Deployment
```bash
# Install Heroku CLI
# Login to Heroku
heroku login

# Create Heroku app
heroku create your-app-name

# Set environment variables
heroku config:set NODE_ENV=production
heroku config:set NEXT_PUBLIC_SITE_URL=https://your-app-name.herokuapp.com

# Deploy
git push heroku main
```

## 🎯 Project Structure

```
caribbean-advantage-tv/
├── app/                    # Next.js App Router
│   ├── components/         # Reusable components
│   ├── gamezone/          # Gaming section
│   ├── live/              # Live TV section
│   ├── radio/             # Radio section
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Homepage
├── public/                # Static assets
├── .env.example           # Environment variables template
├── next.config.js         # Next.js configuration
├── tailwind.config.js     # Tailwind CSS configuration
└── package.json           # Dependencies and scripts
```

## 🌟 Key Components

- **Navigation**: Responsive navigation with language switcher
- **HeroSection**: Animated landing section
- **ServicesSection**: Feature showcase
- **BackgroundAnimation**: Canvas-based particle system
- **LanguageProvider**: Multi-language context
- **FloatingElements**: Animated UI elements

## 🎨 Design Features

- **Glassmorphism Effects**: Modern glass-like UI elements
- **Particle Animations**: Dynamic background particles
- **Smooth Transitions**: Framer Motion animations
- **Caribbean Theme**: Blue/cyan color palette
- **Mobile-First**: Responsive design approach

## 🔧 Configuration

### Environment Variables

See `.env.example` for all available configuration options:

- `NEXT_PUBLIC_SITE_URL`: Your site URL
- `NEXT_PUBLIC_STREAM_URL`: Live stream URL
- `GOOGLE_ANALYTICS_ID`: Analytics tracking
- And more...

## 📱 Pages

- **/** - Homepage with hero and services
- **/live** - Live TV streaming page
- **/gamezone** - Gaming hub
- **/radio** - Online radio player

## 🌍 Multi-language Support

The site supports English and Spanish with:
- Dynamic language switching
- Persistent language preference
- Translated navigation and content

## 🚀 Performance

- **Lighthouse Score**: 95+ across all metrics
- **Core Web Vitals**: Optimized
- **SEO Ready**: Meta tags, sitemap, robots.txt
- **PWA Ready**: Manifest and service worker ready

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🎯 Caribbean Advantage TV

**Channel 99-9** - Your premier destination for Caribbean entertainment!

- 🏝️ **Location**: North Coast, Puerto Rico
- 📺 **Programming**: 24/7 Caribbean content
- 🎵 **Music**: Local and regional artists
- 🎮 **Gaming**: Interactive entertainment
- 📻 **Radio**: Talk shows and music

---

**Built with ❤️ for the Caribbean community**
