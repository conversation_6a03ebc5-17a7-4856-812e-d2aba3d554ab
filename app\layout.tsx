import type { Metadata, Viewport } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { LanguageProvider } from './components/LanguageProvider'
import { MouseFollower } from './components/MouseFollower'
import { BackgroundAnimation } from './components/BackgroundAnimation'

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter'
})

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#3b82f6' },
    { media: '(prefers-color-scheme: dark)', color: '#1e40af' }
  ]
}

export const metadata: Metadata = {
  title: {
    default: 'Caribbean Advantage TV - Channel 99-9',
    template: '%s | Caribbean Advantage TV'
  },
  description: 'Your premier destination for Gaming, Online Radio, and Live TV covering North Coast Puerto Rico. Experience cutting-edge Caribbean entertainment 24/7.',
  keywords: [
    'Caribbean TV',
    'Puerto Rico',
    'Live TV',
    'Online Radio',
    'Gaming',
    'Channel 99-9',
    'North Coast PR',
    'Caribbean Entertainment',
    'Streaming',
    'Caribbean Advantage'
  ],
  authors: [{ name: 'Caribbean Advantage TV' }],
  creator: 'Caribbean Advantage TV',
  publisher: 'Caribbean Advantage TV',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'https://caribbeanadvantage.herokuapp.com'),
  alternates: {
    canonical: '/',
    languages: {
      'en-US': '/en',
      'es-ES': '/es',
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: '/',
    title: 'Caribbean Advantage TV - Channel 99-9',
    description: 'Your premier destination for Gaming, Online Radio, and Live TV covering North Coast Puerto Rico',
    siteName: 'Caribbean Advantage TV',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Caribbean Advantage TV - Channel 99-9',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Caribbean Advantage TV - Channel 99-9',
    description: 'Your premier destination for Gaming, Online Radio, and Live TV covering North Coast Puerto Rico',
    images: ['/og-image.jpg'],
    creator: '@CaribbeanAdvTV',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_VERIFICATION,
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className={`${inter.className} overflow-x-hidden`}>
        <LanguageProvider>
          <BackgroundAnimation />
          <MouseFollower />
          <div className="relative z-10">
            {children}
          </div>
        </LanguageProvider>
      </body>
    </html>
  )
}
