import { MetadataRoute } from 'next'

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: 'Caribbean Advantage TV - Channel 99-9',
    short_name: 'Caribbean TV',
    description: 'Your premier destination for Gaming, Online Radio, and Live TV covering North Coast Puerto Rico',
    start_url: '/',
    display: 'standalone',
    background_color: '#0f172a',
    theme_color: '#3b82f6',
    orientation: 'portrait-primary',
    icons: [
      {
        src: '/favicon.ico',
        sizes: '16x16',
        type: 'image/x-icon',
      },
      {
        src: '/icon-192.png',
        sizes: '192x192',
        type: 'image/png',
      },
      {
        src: '/icon-512.png',
        sizes: '512x512',
        type: 'image/png',
      },
    ],
    categories: ['entertainment', 'news', 'music', 'games'],
    lang: 'en-US',
    dir: 'ltr',
    scope: '/',
  }
}
