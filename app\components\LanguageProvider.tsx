'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'

type Language = 'en' | 'es'

interface LanguageContextType {
  language: Language
  setLanguage: (lang: Language) => void
  t: (key: string) => string
}

const translations = {
  en: {
    // Navigation
    'nav.home': 'Home',
    'nav.live': 'Online TV',
    'nav.gamezone': 'GameZone',
    'nav.radio': 'Online Radio',

    // Common
    'common.watchLive': 'Watch Live',
    'common.playNow': 'Play Now',
    'common.listenNow': 'Listen Now',
    'common.learnMore': 'Learn More',

    // Hero Section
    'hero.title': 'Caribbean Entertainment Revolution',
    'hero.subtitle': 'Experience the future of Caribbean entertainment with cutting-edge gaming, live TV, and radio streaming.',
    'hero.description': 'Your premier destination for Gaming, Online Radio, and Live TV covering North Coast Puerto Rico. Supporting local artists and bringing you the best Caribbean entertainment 24/7.',
    'hero.watchLive': 'Watch Live TV',
    'hero.exploreGames': 'Explore Games',
    'hero.listenRadio': 'Listen Radio',

    // Services
    'services.title': 'Our Premium Services',
    'services.subtitle': 'Discover world-class entertainment across all platforms',

    'services.gaming.title': 'Gaming Zone',
    'services.gaming.subtitle': 'Interactive Entertainment',
    'services.gaming.description': 'Immerse yourself in cutting-edge games, tournaments, and interactive experiences designed for the Caribbean community.',

    'services.tv.title': 'Live Television',
    'services.tv.subtitle': 'Channel 99-9',
    'services.tv.description': 'Watch live Caribbean television with news, entertainment, cultural shows, and exclusive content 24/7.',

    'services.radio.title': 'Online Radio',
    'services.radio.subtitle': 'Caribbean Sounds',
    'services.radio.description': 'Listen to the best Caribbean music, local DJs, cultural programming, and live shows from Puerto Rico.',

    // Live Status
    'live.status': 'Live Now',
    'live.channel': 'Channel 99-9',
    'live.location': 'North Coast PR',
    'live.viewers': 'viewers',

    // Footer
    'footer.description': 'Your premier destination for Gaming, Online Radio, and Live TV covering North Coast Puerto Rico.',
    'footer.quickLinks': 'Quick Links',
    'footer.contact': 'Contact',
    'footer.followUs': 'Follow Us',
    'footer.copyright': '© 2024 Caribbean Advantage TV. All rights reserved.',

    // Additional navigation
    'nav.events': 'Events',
    'nav.contact': 'Contact',
  },
  es: {
    // Navigation
    'nav.home': 'Inicio',
    'nav.live': 'TV en Línea',
    'nav.gamezone': 'Zona de Juegos',
    'nav.radio': 'Radio en Línea',

    // Common
    'common.watchLive': 'Ver en Vivo',
    'common.playNow': 'Jugar Ahora',
    'common.listenNow': 'Escuchar Ahora',
    'common.learnMore': 'Saber Más',

    // Hero Section
    'hero.title': 'Revolución del Entretenimiento Caribeño',
    'hero.subtitle': 'Experimenta el futuro del entretenimiento caribeño con juegos de vanguardia, TV en vivo y transmisión de radio.',
    'hero.description': 'Tu destino principal para Juegos, Radio en Línea y TV en Vivo cubriendo la Costa Norte de Puerto Rico. Apoyando artistas locales y trayéndote el mejor entretenimiento caribeño 24/7.',
    'hero.watchLive': 'Ver TV en Vivo',
    'hero.exploreGames': 'Explorar Juegos',
    'hero.listenRadio': 'Escuchar Radio',

    // Services
    'services.title': 'Nuestros Servicios Premium',
    'services.subtitle': 'Descubre entretenimiento de clase mundial en todas las plataformas',

    'services.gaming.title': 'Zona de Juegos',
    'services.gaming.subtitle': 'Entretenimiento Interactivo',
    'services.gaming.description': 'Sumérgete en juegos de vanguardia, torneos y experiencias interactivas diseñadas para la comunidad caribeña.',

    'services.tv.title': 'Televisión en Vivo',
    'services.tv.subtitle': 'Canal 99-9',
    'services.tv.description': 'Mira televisión caribeña en vivo con noticias, entretenimiento, programas culturales y contenido exclusivo 24/7.',

    'services.radio.title': 'Radio en Línea',
    'services.radio.subtitle': 'Sonidos Caribeños',
    'services.radio.description': 'Escucha la mejor música caribeña, DJs locales, programación cultural y programas en vivo desde Puerto Rico.',

    // Live Status
    'live.status': 'En Vivo Ahora',
    'live.channel': 'Canal 99-9',
    'live.location': 'Costa Norte PR',
    'live.viewers': 'espectadores',

    // Footer
    'footer.description': 'Tu destino principal para Juegos, Radio en Línea y TV en Vivo cubriendo la Costa Norte de Puerto Rico.',
    'footer.quickLinks': 'Enlaces Rápidos',
    'footer.contact': 'Contacto',
    'footer.followUs': 'Síguenos',
    'footer.copyright': '© 2024 Caribbean Advantage TV. Todos los derechos reservados.',

    // Additional navigation
    'nav.events': 'Eventos',
    'nav.contact': 'Contacto',
  }
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

export function LanguageProvider({ children }: { children: ReactNode }) {
  const [language, setLanguageState] = useState<Language>('en')

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') as Language
    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'es')) {
      setLanguageState(savedLanguage)
    }
  }, [])

  const setLanguage = (lang: Language) => {
    setLanguageState(lang)
    localStorage.setItem('language', lang)
  }

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations[typeof language]] || key
  }

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  )
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}
