'use client'

import { motion } from 'framer-motion'
import {
  Play,
  Users,
  Volume2,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Share2
} from 'lucide-react'

import { Navigation } from '../components/Navigation'
import { Footer } from '../components/Footer'
import { FloatingElements } from '../components/FloatingElements'
import { useLanguage } from '../components/LanguageProvider'

export default function LiveTV() {
  const { t } = useLanguage()

  return (
    <main className="relative min-h-screen">
      <Navigation />
      <FloatingElements />

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
        className="relative z-10 pt-24"
      >
        {/* Hero Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-12"
            >
              <h1 className="text-5xl md:text-7xl font-black mb-6 gradient-text">
                {t('nav.live')}
              </h1>
              <p className="text-xl text-blue-200 max-w-3xl mx-auto">
                Watch Caribbean Advantage TV Channel 99-9 live with HD quality streaming
              </p>
            </motion.div>

            {/* Live Player */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="max-w-6xl mx-auto"
            >
              <div className="glass-card overflow-hidden">
                {/* Player Header */}
                <div className="flex items-center justify-between p-4 border-b border-white/20">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse" />
                      <span className="text-white font-semibold">LIVE</span>
                    </div>
                    <div className="text-white">
                      <h3 className="font-semibold">Channel 99-9</h3>
                      <p className="text-sm text-blue-200">Caribbean Advantage TV</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <motion.div
                      className="flex items-center space-x-2 glass-effect px-3 py-1 rounded-lg"
                      whileHover={{ scale: 1.05 }}
                    >
                      <Users className="w-4 h-4 text-pepsi-blue" />
                      <span className="text-white text-sm">1,247 viewers</span>
                    </motion.div>
                  </div>
                </div>

                {/* Video Player */}
                <div className="relative aspect-video bg-black">
                  <iframe
                    src="http://caribbeanadvantage.com/CA-tv.html"
                    className="w-full h-full"
                    allowFullScreen
                    title="Caribbean Advantage TV Live Stream"
                  />

                  {/* Player Controls Overlay */}
                  <motion.div
                    initial={{ opacity: 0 }}
                    whileHover={{ opacity: 1 }}
                    className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 transition-opacity"
                  >
                    <div className="flex items-center space-x-4">
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        className="glass-effect p-4 rounded-full text-white"
                      >
                        <Play className="w-8 h-8" />
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        className="glass-effect p-3 rounded-full text-white"
                      >
                        <Volume2 className="w-6 h-6" />
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        className="glass-effect p-3 rounded-full text-white"
                      >
                        <Settings className="w-6 h-6" />
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        className="glass-effect p-3 rounded-full text-white"
                      >
                        <Maximize className="w-6 h-6" />
                      </motion.button>
                    </div>
                  </motion.div>
                </div>

                {/* Player Footer */}
                <div className="p-4 flex items-center justify-between">
                  <div className="text-white">
                    <p className="font-semibold">Now Playing: Caribbean Entertainment</p>
                    <p className="text-sm text-blue-200">Live from North Coast Puerto Rico</p>
                  </div>

                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="btn-secondary flex items-center space-x-2"
                  >
                    <Share2 className="w-4 h-4" />
                    <span>Share</span>
                  </motion.button>
                </div>
              </div>
            </motion.div>

            {/* Program Schedule */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="mt-16 max-w-4xl mx-auto"
            >
              <h2 className="text-3xl font-bold text-white mb-8 text-center">Today's Schedule</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[
                  { time: '6:00 AM', title: 'Morning News', description: 'Local news and weather' },
                  { time: '9:00 AM', title: 'Caribbean Culture', description: 'Cultural programming' },
                  { time: '12:00 PM', title: 'Midday Update', description: 'News and community events' },
                  { time: '3:00 PM', title: 'Gaming Zone Live', description: 'Interactive gaming show' },
                  { time: '6:00 PM', title: 'Evening News', description: 'Daily news roundup' },
                  { time: '8:00 PM', title: 'Caribbean Music', description: 'Local artists showcase' },
                ].map((program, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 + 0.8 }}
                    className="glass-card hover:glow-effect transition-all duration-300"
                    whileHover={{ scale: 1.02, y: -5 }}
                  >
                    <div className="text-pepsi-blue font-bold text-lg mb-2">{program.time}</div>
                    <h3 className="text-white font-semibold mb-2">{program.title}</h3>
                    <p className="text-blue-200 text-sm">{program.description}</p>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>
        </section>

        <Footer />
      </motion.div>
    </main>
  )
}
