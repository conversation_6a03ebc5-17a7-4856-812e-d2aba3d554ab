'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Radio,
  Play,
  Pause,
  Volume2,
  Heart,
  Share2,
  Music,
  Mic
} from 'lucide-react'

import { Navigation } from '../components/Navigation'
import { Footer } from '../components/Footer'
import { FloatingElements } from '../components/FloatingElements'

export default function RadioPage() {
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentShow] = useState('Caribbean Vibes')
  const [volume] = useState(75)

  const shows = [
    {
      time: '6:00 AM',
      title: 'Morning Caribbean',
      host: 'DJ Sunrise',
      description: 'Start your day with the best Caribbean music',
      genre: 'Mixed'
    },
    {
      time: '10:00 AM',
      title: 'Salsa Sessions',
      host: '<PERSON>',
      description: 'The hottest salsa tracks from across the Caribbean',
      genre: 'Salsa'
    },
    {
      time: '2:00 PM',
      title: 'Reggaeton Power',
      host: 'DJ Fuego',
      description: 'Non-stop reggaeton hits and new releases',
      genre: 'Reggaeton'
    },
    {
      time: '6:00 PM',
      title: 'Caribbean Vibes',
      host: '<PERSON>',
      description: 'Smooth Caribbean sounds for your evening',
      genre: 'Chill'
    },
    {
      time: '9:00 PM',
      title: 'Bachata Nights',
      host: '<PERSON>',
      description: 'Romantic bachata for your night',
      genre: 'Bachata'
    },
    {
      time: '12:00 AM',
      title: 'Late Night Mix',
      host: 'DJ Nocturno',
      description: 'Eclectic mix for night owls',
      genre: 'Mixed'
    }
  ]

  return (
    <main className="relative min-h-screen">
      <Navigation />
      <FloatingElements />

      <div className="relative z-10 pt-20">
        {/* Hero Section */}
        <motion.section
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="container mx-auto px-4 py-8 text-center"
        >
          <motion.div
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, duration: 0.6 }}
            className="mb-8"
          >
            <Radio className="w-16 h-16 mx-auto mb-4 text-pepsi-blue" />
            <h1 className="text-4xl md:text-5xl font-bold mb-4 gradient-text">
              📻 Caribbean Radio
            </h1>
            <p className="text-lg text-gray-300 max-w-2xl mx-auto">
              Tune in to the best Caribbean music, talk shows, and local content 24/7
            </p>
          </motion.div>
        </motion.section>

        {/* Main Content with Sidebars */}
        <div className="container mx-auto px-4 py-4">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">

            {/* Left Promotional Banner */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4, duration: 0.6 }}
              className="lg:col-span-2 space-y-4"
            >
              {/* Music Promotion */}
              <div className="glass-card p-3 text-center hover:glow-effect transition-all duration-300">
                <div className="bg-gradient-to-br from-pepsi-blue to-pepsi-lightBlue rounded-lg p-3 mb-2">
                  <Music className="w-6 h-6 mx-auto text-white mb-1" />
                  <h3 className="text-white font-bold text-xs">New Music</h3>
                </div>
                <p className="text-xs text-gray-300 mb-2">Latest Caribbean hits!</p>
                <button className="bg-pepsi-blue hover:bg-pepsi-darkBlue text-white text-xs px-2 py-1 rounded transition-colors">
                  Listen
                </button>
              </div>

              {/* Podcast Promotion */}
              <div className="glass-card p-3 text-center hover:glow-effect transition-all duration-300">
                <div className="bg-gradient-to-br from-green-500 to-green-600 rounded-lg p-3 mb-2">
                  <Mic className="w-6 h-6 mx-auto text-white mb-1" />
                  <h3 className="text-white font-bold text-xs">Podcasts</h3>
                </div>
                <p className="text-xs text-gray-300 mb-2">Caribbean talk shows!</p>
                <button className="bg-green-500 hover:bg-green-600 text-white text-xs px-2 py-1 rounded transition-colors">
                  Explore
                </button>
              </div>
            </motion.div>

            {/* Main Radio Player */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.6 }}
              className="lg:col-span-8"
            >
              {/* Radio Player */}
              <div className="glass-card p-4 mb-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold text-white flex items-center">
                    <Radio className="w-5 h-5 mr-2 text-pepsi-blue" />
                    Caribbean Radio Player
                  </h2>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
                    <span className="text-white font-semibold text-sm">LIVE</span>
                  </div>
                </div>

                {/* Radio Iframe */}
                <div className="relative bg-black rounded-lg overflow-hidden mb-4" style={{ aspectRatio: '16/9' }}>
                  <iframe
                    src="https://caribbeanadvantage.com/index.php/online-radio"
                    className="w-full h-full border-0"
                    title="Caribbean Advantage Radio - Live Stream"
                    allowFullScreen
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent pointer-events-none" />
                </div>

                {/* Now Playing Info */}
                <div className="bg-gradient-to-r from-pepsi-darkBlue/20 to-pepsi-blue/20 p-4 rounded-lg mb-4">
                  <div className="flex items-center space-x-4">
                    <motion.div
                      className="w-12 h-12 bg-gradient-to-r from-pepsi-blue to-pepsi-lightBlue rounded-xl flex items-center justify-center"
                      animate={{ rotate: isPlaying ? 360 : 0 }}
                      transition={{ duration: 4, repeat: isPlaying ? Infinity : 0, ease: "linear" }}
                    >
                      <Radio className="w-6 h-6 text-white" />
                    </motion.div>
                    <div className="flex-1">
                      <h3 className="text-lg font-bold text-white">Now Playing: {currentShow}</h3>
                      <p className="text-pepsi-lightBlue text-sm">Caribbean Advantage Radio - Channel 99-9</p>
                    </div>
                  </div>
                </div>

                {/* Player Controls */}
                <div className="flex items-center justify-center space-x-4 mb-4">
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    className="glass-card p-2 rounded-full text-white hover:bg-white/20 transition-colors"
                  >
                    <Heart className="w-4 h-4" />
                  </motion.button>

                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => setIsPlaying(!isPlaying)}
                    className="bg-gradient-to-r from-pepsi-blue to-pepsi-lightBlue hover:from-pepsi-darkBlue hover:to-pepsi-blue p-3 rounded-full text-white transition-all duration-300"
                  >
                    {isPlaying ? (
                      <Pause className="w-5 h-5" />
                    ) : (
                      <Play className="w-5 h-5" />
                    )}
                  </motion.button>

                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    className="glass-card p-2 rounded-full text-white hover:bg-white/20 transition-colors"
                  >
                    <Volume2 className="w-4 h-4" />
                  </motion.button>

                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    className="glass-card p-2 rounded-full text-white hover:bg-white/20 transition-colors"
                  >
                    <Share2 className="w-4 h-4" />
                  </motion.button>
                </div>

                {/* Volume Slider */}
                <div className="flex items-center space-x-3">
                  <Volume2 className="w-4 h-4 text-pepsi-blue" />
                  <div className="flex-1 h-2 bg-white/20 rounded-full overflow-hidden">
                    <motion.div
                      className="h-full bg-gradient-to-r from-pepsi-blue to-pepsi-lightBlue rounded-full"
                      style={{ width: `${volume}%` }}
                    />
                  </div>
                  <span className="text-white text-sm">{volume}%</span>
                </div>
              </div>

              {/* Program Schedule */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {shows.slice(0, 4).map((show, index) => (
                  <div key={index} className="glass-card p-3 hover:glow-effect transition-all duration-300 cursor-pointer">
                    <div className="flex items-center justify-between mb-2">
                      <div className="text-pepsi-blue font-bold text-sm">{show.time}</div>
                      <span className="text-xs px-2 py-1 rounded-full bg-pepsi-blue/30 text-pepsi-lightBlue">
                        {show.genre}
                      </span>
                    </div>
                    <h3 className="text-white font-bold text-sm mb-1">{show.title}</h3>
                    <p className="text-gray-400 text-xs">{show.host}</p>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Right Promotional Banner */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.8, duration: 0.6 }}
              className="lg:col-span-2 space-y-4"
            >
              {/* Live Shows Promotion */}
              <div className="glass-card p-3 text-center hover:glow-effect transition-all duration-300">
                <div className="bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg p-3 mb-2">
                  <div className="flex items-center justify-center mb-1">
                    <div className="w-2 h-2 bg-purple-300 rounded-full animate-pulse mr-2"></div>
                    <span className="text-white font-bold text-xs">LIVE</span>
                  </div>
                  <h3 className="text-white font-bold text-xs">Talk Shows</h3>
                </div>
                <p className="text-xs text-gray-300 mb-2">Join our live discussions!</p>
                <button className="bg-purple-500 hover:bg-purple-600 text-white text-xs px-2 py-1 rounded transition-colors">
                  Join Live
                </button>
              </div>

              {/* Music Request */}
              <div className="glass-card p-3 text-center hover:glow-effect transition-all duration-300">
                <div className="bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg p-3 mb-2">
                  <Music className="w-6 h-6 mx-auto text-white mb-1" />
                  <h3 className="text-white font-bold text-xs">Request Song</h3>
                </div>
                <p className="text-xs text-gray-300 mb-2">Request your favorite song!</p>
                <button className="bg-orange-500 hover:bg-orange-600 text-white text-xs px-2 py-1 rounded transition-colors">
                  Request
                </button>
              </div>

              {/* DJ Schedule */}
              <div className="glass-card p-3 text-center hover:glow-effect transition-all duration-300">
                <div className="bg-gradient-to-br from-green-500 to-green-600 rounded-lg p-3 mb-2">
                  <Mic className="w-6 h-6 mx-auto text-white mb-1" />
                  <h3 className="text-white font-bold text-xs">DJ Schedule</h3>
                </div>
                <p className="text-xs text-gray-300 mb-2">See who's on air today!</p>
                <button className="bg-green-500 hover:bg-green-600 text-white text-xs px-2 py-1 rounded transition-colors">
                  View Schedule
                </button>
              </div>
            </motion.div>
          </div>
        </div>

        <Footer />
      </div>
    </main>
  )
}
