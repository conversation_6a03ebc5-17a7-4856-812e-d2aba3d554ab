#!/bin/bash

# Caribbean Advantage TV - Heroku Deployment Script
# This script automates the deployment process to Heroku

echo "🌴 Caribbean Advantage TV - Channel 99-9 Deployment Script"
echo "=========================================================="

# Check if Heroku CLI is installed
if ! command -v heroku &> /dev/null; then
    echo "❌ Heroku CLI is not installed. Please install it first:"
    echo "   https://devcenter.heroku.com/articles/heroku-cli"
    exit 1
fi

# Check if user is logged in to Heroku
if ! heroku auth:whoami &> /dev/null; then
    echo "❌ Please login to Heroku first:"
    echo "   heroku login"
    exit 1
fi

echo "✅ Heroku CLI detected and user is logged in"

# Get app name from user
read -p "Enter your Heroku app name (or press Enter for 'caribbean-advantage-tv'): " APP_NAME
APP_NAME=${APP_NAME:-caribbean-advantage-tv}

echo "📦 Preparing deployment for app: $APP_NAME"

# Check if app exists, if not create it
if ! heroku apps:info $APP_NAME &> /dev/null; then
    echo "🆕 Creating new Heroku app: $APP_NAME"
    heroku create $APP_NAME
    
    if [ $? -ne 0 ]; then
        echo "❌ Failed to create Heroku app. The name might be taken."
        echo "   Please try a different name."
        exit 1
    fi
else
    echo "✅ Heroku app $APP_NAME already exists"
fi

# Set environment variables
echo "🔧 Setting environment variables..."
heroku config:set NODE_ENV=production --app $APP_NAME
heroku config:set NEXT_PUBLIC_SITE_URL=https://$APP_NAME.herokuapp.com --app $APP_NAME
heroku config:set NEXT_PUBLIC_SITE_NAME="Caribbean Advantage TV" --app $APP_NAME
heroku config:set NEXT_PUBLIC_CHANNEL_NUMBER="99-9" --app $APP_NAME
heroku config:set NEXT_TELEMETRY_DISABLED=1 --app $APP_NAME

echo "✅ Environment variables set"

# Add Heroku remote if it doesn't exist
if ! git remote get-url heroku &> /dev/null; then
    echo "🔗 Adding Heroku remote..."
    heroku git:remote -a $APP_NAME
fi

# Build the project locally to check for errors
echo "🏗️  Building project locally..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Local build failed. Please fix the errors before deploying."
    exit 1
fi

echo "✅ Local build successful"

# Commit any uncommitted changes
if ! git diff-index --quiet HEAD --; then
    echo "📝 Committing changes..."
    git add .
    git commit -m "Prepare for deployment - $(date)"
fi

# Deploy to Heroku
echo "🚀 Deploying to Heroku..."
git push heroku main

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Deployment successful!"
    echo "🌐 Your app is available at: https://$APP_NAME.herokuapp.com"
    echo "📊 View logs: heroku logs --tail --app $APP_NAME"
    echo "⚙️  Open dashboard: heroku open --app $APP_NAME"
    echo ""
    echo "🌴 Caribbean Advantage TV - Channel 99-9 is now live!"
else
    echo "❌ Deployment failed. Check the logs:"
    echo "   heroku logs --tail --app $APP_NAME"
    exit 1
fi
