@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200;
  }

  body {
    @apply text-white min-h-screen;
    background: linear-gradient(135deg, #002733 0%, #003f66 25%, #005799 50%, #006fcc 75%, #0087ff 100%);
    background-attachment: fixed;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .glass-effect {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .glass-card {
    @apply glass-effect rounded-2xl p-6 shadow-2xl;
  }

  .btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg;
  }

  .btn-secondary {
    @apply glass-effect text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 hover:bg-white/20;
  }

  .nav-link {
    @apply text-white/90 hover:text-white transition-all duration-300 font-medium relative;
  }

  .nav-link::after {
    content: '';
    @apply absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-400 to-cyan-400 transition-all duration-300;
  }

  .nav-link:hover::after,
  .nav-link.active::after {
    @apply w-full;
  }

  .floating-card {
    @apply transform transition-all duration-500 hover:scale-105 hover:-translate-y-2;
  }

  .glow-effect {
    @apply shadow-2xl;
    box-shadow: 0 0 30px rgba(0, 135, 255, 0.3);
  }

  .glow-effect:hover {
    box-shadow: 0 0 50px rgba(0, 135, 255, 0.5);
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
  }

  .animate-float-slow {
    animation: float 8s ease-in-out infinite;
  }

  .animate-float-medium {
    animation: float 6s ease-in-out infinite;
  }

  .animate-float-fast {
    animation: float 4s ease-in-out infinite;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.7);
}

/* Loading animation */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.shimmer {
  position: relative;
  overflow: hidden;
}

.shimmer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: shimmer 2s infinite;
}

/* Particle effects */
@keyframes particle-float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

.particle {
  animation: particle-float 4s ease-in-out infinite;
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #60a5fa, #34d399, #fbbf24);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Neon glow effect */
.neon-glow {
  text-shadow:
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 15px currentColor,
    0 0 20px currentColor;
}

/* Holographic effect */
.holographic {
  background: linear-gradient(
    45deg,
    #ff006e,
    #8338ec,
    #3a86ff,
    #06ffa5,
    #ffbe0b,
    #fb5607
  );
  background-size: 300% 300%;
  animation: holographic 3s ease infinite;
}

@keyframes holographic {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}
