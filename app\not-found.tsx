'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { Navigation } from './components/Navigation'
import { Footer } from './components/Footer'
import { FloatingElements } from './components/FloatingElements'
import { Home, ArrowLeft, Tv, Radio, Gamepad2 } from 'lucide-react'
import { useLanguage } from './components/LanguageProvider'

export default function NotFound() {
  const { t } = useLanguage()

  return (
    <main className="relative min-h-screen">
      <Navigation />
      <FloatingElements />
      
      <div className="relative z-10 min-h-screen flex items-center justify-center px-4">
        <div className="text-center max-w-2xl mx-auto">
          {/* 404 Animation */}
          <motion.div
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="mb-8"
          >
            <div className="text-8xl md:text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-500 to-cyan-400 mb-4">
              404
            </div>
            <motion.div
              animate={{ rotate: [0, 10, -10, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="text-6xl mb-4"
            >
              📺
            </motion.div>
          </motion.div>

          {/* Error Message */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.6 }}
            className="mb-8"
          >
            <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Oops! Channel Not Found
            </h1>
            <p className="text-gray-300 text-lg mb-6">
              The page you're looking for seems to have gone off the air. 
              Let's get you back to our main programming!
            </p>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8"
          >
            <Link href="/">
              <motion.button
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="glass-card px-6 py-3 rounded-xl text-white font-semibold flex items-center space-x-2 hover:glow-effect transition-all duration-300"
              >
                <Home className="w-5 h-5" />
                <span>Back to Home</span>
              </motion.button>
            </Link>

            <Link href="/live">
              <motion.button
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="glass-card px-6 py-3 rounded-xl text-white font-semibold flex items-center space-x-2 hover:glow-effect transition-all duration-300"
              >
                <Tv className="w-5 h-5" />
                <span>Watch Live TV</span>
              </motion.button>
            </Link>
          </motion.div>

          {/* Quick Links */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.9, duration: 0.6 }}
            className="grid grid-cols-1 sm:grid-cols-3 gap-4 max-w-md mx-auto"
          >
            <Link href="/gamezone">
              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                className="glass-card p-4 rounded-xl text-center hover:glow-effect transition-all duration-300 cursor-pointer"
              >
                <Gamepad2 className="w-8 h-8 mx-auto mb-2 text-blue-400" />
                <span className="text-white font-medium">GameZone</span>
              </motion.div>
            </Link>

            <Link href="/radio">
              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                className="glass-card p-4 rounded-xl text-center hover:glow-effect transition-all duration-300 cursor-pointer"
              >
                <Radio className="w-8 h-8 mx-auto mb-2 text-green-400" />
                <span className="text-white font-medium">Radio</span>
              </motion.div>
            </Link>

            <Link href="/live">
              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                className="glass-card p-4 rounded-xl text-center hover:glow-effect transition-all duration-300 cursor-pointer"
              >
                <Tv className="w-8 h-8 mx-auto mb-2 text-purple-400" />
                <span className="text-white font-medium">Live TV</span>
              </motion.div>
            </Link>
          </motion.div>

          {/* Fun Animation */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.2, duration: 0.6 }}
            className="mt-12"
          >
            <motion.div
              animate={{ 
                y: [0, -10, 0],
                rotate: [0, 5, -5, 0]
              }}
              transition={{ 
                duration: 3, 
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="text-4xl"
            >
              🌴
            </motion.div>
            <p className="text-gray-400 text-sm mt-2">
              Caribbean Advantage TV - Channel 99-9
            </p>
          </motion.div>
        </div>
      </div>

      <Footer />
    </main>
  )
}
