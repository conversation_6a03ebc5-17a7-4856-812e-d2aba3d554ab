@echo off
setlocal enabledelayedexpansion

echo 🌴 Caribbean Advantage TV - Channel 99-9 Deployment Script
echo ==========================================================

REM Check if Heroku CLI is installed
heroku --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Heroku CLI is not installed. Please install it first:
    echo    https://devcenter.heroku.com/articles/heroku-cli
    pause
    exit /b 1
)

REM Check if user is logged in to Heroku
heroku auth:whoami >nul 2>&1
if errorlevel 1 (
    echo ❌ Please login to Heroku first:
    echo    heroku login
    pause
    exit /b 1
)

echo ✅ Heroku CLI detected and user is logged in

REM Get app name from user
set /p APP_NAME="Enter your Heroku app name (or press Enter for 'caribbean-advantage-tv'): "
if "%APP_NAME%"=="" set APP_NAME=caribbean-advantage-tv

echo 📦 Preparing deployment for app: %APP_NAME%

REM Check if app exists, if not create it
heroku apps:info %APP_NAME% >nul 2>&1
if errorlevel 1 (
    echo 🆕 Creating new Heroku app: %APP_NAME%
    heroku create %APP_NAME%
    if errorlevel 1 (
        echo ❌ Failed to create Heroku app. The name might be taken.
        echo    Please try a different name.
        pause
        exit /b 1
    )
) else (
    echo ✅ Heroku app %APP_NAME% already exists
)

REM Set environment variables
echo 🔧 Setting environment variables...
heroku config:set NODE_ENV=production --app %APP_NAME%
heroku config:set NEXT_PUBLIC_SITE_URL=https://%APP_NAME%.herokuapp.com --app %APP_NAME%
heroku config:set NEXT_PUBLIC_SITE_NAME="Caribbean Advantage TV" --app %APP_NAME%
heroku config:set NEXT_PUBLIC_CHANNEL_NUMBER="99-9" --app %APP_NAME%
heroku config:set NEXT_TELEMETRY_DISABLED=1 --app %APP_NAME%

echo ✅ Environment variables set

REM Add Heroku remote if it doesn't exist
git remote get-url heroku >nul 2>&1
if errorlevel 1 (
    echo 🔗 Adding Heroku remote...
    heroku git:remote -a %APP_NAME%
)

REM Build the project locally to check for errors
echo 🏗️  Building project locally...
call npm run build

if errorlevel 1 (
    echo ❌ Local build failed. Please fix the errors before deploying.
    pause
    exit /b 1
)

echo ✅ Local build successful

REM Commit any uncommitted changes
git diff-index --quiet HEAD -- >nul 2>&1
if errorlevel 1 (
    echo 📝 Committing changes...
    git add .
    git commit -m "Prepare for deployment - %date% %time%"
)

REM Deploy to Heroku
echo 🚀 Deploying to Heroku...
git push heroku main

if not errorlevel 1 (
    echo.
    echo 🎉 Deployment successful!
    echo 🌐 Your app is available at: https://%APP_NAME%.herokuapp.com
    echo 📊 View logs: heroku logs --tail --app %APP_NAME%
    echo ⚙️  Open dashboard: heroku open --app %APP_NAME%
    echo.
    echo 🌴 Caribbean Advantage TV - Channel 99-9 is now live!
) else (
    echo ❌ Deployment failed. Check the logs:
    echo    heroku logs --tail --app %APP_NAME%
    pause
    exit /b 1
)

pause
